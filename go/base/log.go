package base

import (
	"fmt"
	"os"
	"strings"

	log "github.com/sirupsen/logrus"
)

const FieldStackTrace = "StackTrace"

var host string
var appName string

func init() {
	host = getHost()
	appName = getEnv("CANARY_APP_NAME", "gh-ost")
}

func getHost() string {
	hostname := ""
	var err error
	h := os.Getenv("HOST")
	if h != "" {
		hostname = h
	} else {
		hostname, err = os.Hostname()
		if err != nil {
			return ""
		}
	}
	return hostname
}

func getEnv(key string, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

type CustomFormatter struct {
	WorkId          int64
	SubOrderId      int64
	Host            string
	Port            int
	Database        string
	Table           string
	PrintStackTrace bool
}

func (f *CustomFormatter) Format(entry *log.Entry) ([]byte, error) {
	caller := ""
	if entry.HasCaller() {
		caller = fmt.Sprintf("%s:%d", entry.Caller.File, entry.Caller.Line)
	}

	logString := []byte(fmt.Sprintf("[%s][%s][%s][%s][%d][][%s] : [%d] [%d] [%s:%d] [%s] [%s] : %s\n",
		host, appName, entry.Time.Format("2006-01-02 15:04:05.000"),
		strings.ToUpper(entry.Level.String()), os.Getpid(), caller, f.WorkId, f.SubOrderId, f.Host, f.Port, f.Database, f.Table, entry.Message))
	if value, ok := entry.Data[FieldStackTrace]; ok && f.PrintStackTrace && entry.Level <= log.ErrorLevel {
		if byteVal, okk := value.([]byte); okk {
			logString = append(logString, byteVal...)
		} else {
			logString = append(logString, []byte("stack trace get failed, check whether correct use log method")...)
		}
	}
	return logString, nil
}
