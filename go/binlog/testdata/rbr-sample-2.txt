drop table if exists samplet;
create table samplet(id int primary key, license int, name varchar(64), b tinyblob, unique key license_uidx(license)) engine=innodb;
insert into samplet (id, license, name) values (1,1,'a');
insert into samplet (id, license, name) values (2,2,'extended'),(3,3,'extended');
begin;
insert into samplet (id, license, name) values (4,4,'transaction');
insert into samplet (id, license, name) values (5,5,'transaction');
insert into samplet (id, license, name) values (6,6,'transaction');
commit;
update samplet set name='update' where id=5;
replace into samplet (id, license, name) values (2,4,'replaced 2,4');
insert into samplet (id, license, name, b) values (7,7,'7', x'89504E470D0A1A0A0000000D494844520000001000000010080200000090916836000000017352474200AECE1CE90000000467414D410000B18F0BFC6105000000097048597300000EC300000EC301C76FA8640000001E49444154384F6350DAE843126220493550F1A80662426C349406472801006AC91F1040F796BD0000000049454E44AE426082');
insert into samplet (id, license, name) values (8,8,'8');
delete from samplet where id >= 7;
insert into samplet (id, license, name) values (9,9,'9');
begin;
update samplet set name='update 9', b=x'89504E470D0A1A0A0000000D494844520000001000000010080200000090916836000000017352474200AECE1CE90000000467414D410000B18F0BFC6105000000097048597300000EC300000EC301C76FA8640000001E49444154384F6350DAE843126220493550F1A80662426C349406472801006AC91F1040F796BD0000000049454E44AE426082' where id=9;
update samplet set name='update 9b' where id=9;
delete from samplet where license=3;
insert into samplet (id, license, name) values (10,10,'10');
commit;
update samplet set name='update 5,6' where id in (5,6);
begin;
delete from samplet where id=5;
rollback;
