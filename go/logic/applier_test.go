/*
   Copyright 2016 GitHub Inc.
	 See https://github.com/github/gh-ost/blob/master/LICENSE
*/

package logic

import (
	"strings"
	"testing"

	test "github.com/outbrain/golib/tests"

	"github.com/github/gh-ost/go/base"
)

func TestHandleRowFormatInCreateStatement(t *testing.T) {
	migrationContext := base.NewMigrationContext()
	migrationContext.DatabaseName = "test_db"
	migrationContext.OriginalTableName = "test_table"
	migrationContext.RowFormat = "COMPRESSED"

	applier := &Applier{
		migrationContext: migrationContext,
	}

	// Test case 1: CREATE TABLE without existing ROW_FORMAT
	createStatement1 := "CREATE TABLE `test_table` (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `name` varchar(255) DEFAULT NULL,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=utf8"

	result1, err := applier.handleRowFormatInCreateStatement(createStatement1)
	test.S(t).ExpectNil(err)
	test.S(t).ExpectTrue(strings.Contains(result1, "ROW_FORMAT=COMPRESSED"))

	// Test case 2: CREATE TABLE with existing ROW_FORMAT
	createStatement2 := "CREATE TABLE `test_table` (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `name` varchar(255) DEFAULT NULL,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB ROW_FORMAT=DYNAMIC DEFAULT CHARSET=utf8"

	result2, err := applier.handleRowFormatInCreateStatement(createStatement2)
	test.S(t).ExpectNil(err)
	test.S(t).ExpectTrue(strings.Contains(result2, "ROW_FORMAT=COMPRESSED"))
	test.S(t).ExpectFalse(strings.Contains(result2, "ROW_FORMAT=DYNAMIC"))

	// Test case 3: CREATE TABLE with ENGINE at the end
	createStatement3 := "CREATE TABLE `test_table` (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  `name` varchar(255) DEFAULT NULL,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB"

	result3, err := applier.handleRowFormatInCreateStatement(createStatement3)
	test.S(t).ExpectNil(err)
	test.S(t).ExpectTrue(strings.Contains(result3, "ROW_FORMAT=COMPRESSED ENGINE=InnoDB"))
}

func TestModifyCreateTableForGhost(t *testing.T) {
	migrationContext := base.NewMigrationContext()
	migrationContext.DatabaseName = "test_db"
	migrationContext.OriginalTableName = "test_table"
	migrationContext.MyRandNum = "123456"
	migrationContext.RowFormat = "COMPRESSED"

	applier := &Applier{
		migrationContext: migrationContext,
	}

	// Test modifying table name
	originalCreate := "CREATE TABLE `test_table` (\n  `id` int(11) NOT NULL AUTO_INCREMENT,\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB"

	result, err := applier.modifyCreateTableForGhost(originalCreate)
	test.S(t).ExpectNil(err)

	ghostTableName := migrationContext.GetGhostTableName()
	expectedTableRef := "`test_db`.`" + ghostTableName + "`"
	test.S(t).ExpectTrue(strings.Contains(result, expectedTableRef))
	test.S(t).ExpectTrue(strings.Contains(result, "ROW_FORMAT=COMPRESSED"))
}
