2025-04-28 12:36:20 INFO starting gh-ost 1.0.46
2025-04-28 12:36:20 INFO Migrating `pdd_louisiana_58`.`virtual_order_message_3752`
2025-04-28 12:36:20 INFO connection validated on ************:3306
2025-04-28 12:36:20 INFO User has SUPER, REPLICATION SLAVE privileges, and has ALL privileges on `pdd_louisiana_58`.*
2025-04-28 12:36:20 INFO binary logs validated on ************:3306
2025-04-28 12:36:20 INFO Restarting replication on ************:3306 to make sure binlog settings apply to replication thread
2025-04-28 12:36:20 INFO Inspector initiated on ************:3306, version 5.7.18-txsql-log
2025-04-28 12:36:04 INFO Ghost table Analyze
2025-04-28 12:36:04 INFO Ghost table an complete
Copy: 258839/258839 100.0%; Applied: 1; Backlog: 0/1000; Time: 1m3s(total), 1m2s(copy); streamer: mysql-bin.008912:8788423; State: migrating; ETA: due
2025-04-28 12:36:04 INFO Grabbing voluntary lock: gh-ost.197658451.lock
2025-04-28 12:36:04 INFO Setting LOCK timeout as 2 seconds
2025-04-28 12:36:04 INFO Looking for magic cut-over table
2025-04-28 12:36:04 INFO Creating magic cut-over table `pdd_louisiana_58`.`zz__virtual_order_message_3751_1745814901_del`
2025-04-28 12:36:04 INFO Magic cut-over table created
2025-04-28 12:36:04 INFO Locking `pdd_louisiana_58`.`virtual_order_message_3751`, `pdd_louisiana_58`.`zz__virtual_order_message_3751_1745814901_del`
2025-04-28 12:36:04 INFO Tables locked
2025-04-28 12:36:04 INFO pdd_louisiana_58.virtual_order_message_3751 before lock gtid point file:mysql-bin.008912 position:8789506
2025-04-28 12:36:04 INFO Getting nothing in the write queue okToUnlockTable. Sleeping...
2025-04-28 12:36:04 INFO Session locking original & magic tables is 197658451
2025-04-28 12:36:04 INFO Writing changelog state: AllEventsUpToLockProcessed:1745814964308415795
2025-04-28 12:36:04 INFO Intercepted changelog state AllEventsUpToLockProcessed
2025-04-28 12:36:04 INFO Handled changelog state AllEventsUpToLockProcessed
2025-04-28 12:36:04 INFO Waiting for events up to lock
2025-04-28 12:36:05 INFO Waiting for events up to lock: got AllEventsUpToLockProcessed:1745814964308415795
2025-04-28 12:36:15 INFO Done waiting for events up to lock; duration=10.820098558s
2025-04-28 12:36:09 ERROR not get inform on the write queue okToUnlockTable. 5 timeout exit
2025-04-28 12:36:15 INFO Will now proceed to drop magic table and unlock tables
2025-04-28 12:36:15 INFO Dropping magic cut-over table
2025-04-28 12:36:15 FATAL 2025-04-28 12:36:09 ERROR not get inform on the write queue okToUnlockTable. 5 timeout exit
Copy: 258839/258839 100.0%; Applied: 1; Backlog: 0/1000; Time: 1m5s(total), 1m2s(copy); streamer: mysql-bin.008912:8818230; State: migrating; ETA: due

2025-04-28 12:36:20 INFO starting gh-ost 1.0.46



////////////




2025-05-14 12:00:18 INFO Done waiting for events up to lock; duration=12.413866604s
Copy: 191316/191316 100.0%; Applied: 0; Backlog: 0/1000; Time: 11s(total), 8s(copy); streamer: mysql-bin.034876:123782408; State: migrating; ETA: due
Copy: 191316/191316 100.0%; Applied: 0; Backlog: 0/1000; Time: 12s(total), 8s(copy); streamer: mysql-bin.034876:123788035; State: migrating; ETA: due
Copy: 191316/191316 100.0%; Applied: 0; Backlog: 0/1000; Time: 13s(total), 8s(copy); streamer: mysql-bin.034876:123794209; State: migrating; ETA: due
# Migrating `pickers_21`.`sms_das_first_pick_detail_11`; Ghost table is `pickers_21`.`zz__sms_das_first_pick_detail_11_1747195197_gho`
2025-05-14 12:00:11 ERROR not get inform on the write queue okToUnlockTable. 5 timeout exit