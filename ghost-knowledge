
1、1380000 数据，300s 拷贝完成
CREATE TABLE `htd_test_many_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'CgI2WRIIcXZyVUVkRDEacIBxBBc1V5dVSkIvN6wjugplbrff5xbuDSw1vsjHSveOGgN1Pbl7bdzMhkb9+E6K6+NquCuxD0dCpCnAlGM2jmyEaANGKj6EJBPEVnjRhw592G5jdieNlbOA9sK1yKMtpqkWncYMBT7DVUKmJcOTxjYwAg==',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'CgI2WRIIcXZyVUVkRDEaEAhqRDYi2jP79Pdsotx6wy4wAg==',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT 'CgI2WRIIcXZyVUVkRDEaEOBa5Fchg+lMNLzoxYWsSEIwAg==',
  `is_deleted` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'CgI2WRIIcXZyVUVkRDEaIKQ1H3yMb0Jw0Xt6HfuCEvDUAbSU4i4IRFOKIMI4i62/MAI=',
  `varchar_10` varchar(10) NOT NULL DEFAULT '' COMMENT 'CgI2WRIIcXZyVUVkRDEaENp/9IixXSYmhvG+ER2dkV8wAg==',
  `smallint` smallint(6) NOT NULL DEFAULT '0' COMMENT 'CgI2WRIIcXZyVUVkRDEaENp/9IixXSYmhvG+ER2dkV8wAg==',
  `first_compelete_time` varchar(20) NOT NULL DEFAULT '2025-04-25 16:14:45' COMMENT 'CgI2WRIIcXZyVUVkRDEaIAcXrkKgJLTrKaXRiA7dDfrD8TRQjUyi+/1yo75LRhkjMAI=',
  `second_compelete_time` varchar(20) NOT NULL DEFAULT '2025-04-25 16:14:45' COMMENT 'CgI2WRIIcXZyVUVkRDEaIAJ5AUryrDRHzwwas7oY5craTjN4VtfOsP+XUf9bMZe5MAI=',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1384301 DEFAULT CHARSET=utf8mb4 COMMENT='CgI2WRIIcXZyVUVkRDEaEL1RnjzZFvm30MYt4WjB1L4wAg=='



2、gh-ost 数据应用方式
copy: insert ignore into %s.%s (%s) (select %s from %s.%s force index (%s) where (%s and %s) lock in share mode)
apply delete: delete from %s.%s where %s
apply insert: replace into %s.%s (%s) values (%s)
apply update: update %s.%s set %s where %s