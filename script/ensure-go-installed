#!/bin/bash

PREFERRED_GO_VERSION=go1.9.2
SUPPORTED_GO_VERSIONS='go1.[89]'

GO_PKG_DARWIN=${PREFERRED_GO_VERSION}.darwin-amd64.pkg
GO_PKG_DARWIN_SHA=8b4f6ae6deae1150d2e341d02c247fd18a99af387516540eeb84702ffd76d3a1

GO_PKG_LINUX=${PREFERRED_GO_VERSION}.linux-amd64.tar.gz
GO_PKG_LINUX_SHA=de874549d9a8d8d8062be05808509c09a88a248e77ec14eb77453530829ac02b

export ROOTDIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." && pwd )"
cd $ROOTDIR

# If Go isn't installed globally, setup environment variables for local install.
if [ -z "$(which go)" ] || [ -z "$(go version | grep "$SUPPORTED_GO_VERSIONS")" ]; then
  GODIR="$ROOTDIR/.vendor/go19"

  if [ $(uname -s) = "Darwin" ]; then
    export GOROOT="$GODIR/usr/local/go"
  else
    export GOROOT="$GODIR/go"
  fi

  export PATH="$GOROOT/bin:$PATH"
fi

# Check if local install exists, and install otherwise.
if [ -z "$(which go)" ] || [ -z "$(go version | grep "$SUPPORTED_GO_VERSIONS")" ]; then
  [ -d "$GODIR" ] && rm -rf $GODIR
  mkdir -p "$GODIR"
  cd "$GODIR";

  if [ $(uname -s) = "Darwin" ]; then
    curl -L -O https://storage.googleapis.com/golang/$GO_PKG_DARWIN
    shasum -a256 $GO_PKG_DARWIN | grep $GO_PKG_DARWIN_SHA
    xar -xf $GO_PKG_DARWIN
    cpio -i < com.googlecode.go.pkg/Payload
  else
    curl -L -O https://storage.googleapis.com/golang/$GO_PKG_LINUX
    shasum -a256 $GO_PKG_LINUX | grep $GO_PKG_LINUX_SHA
    tar xf $GO_PKG_LINUX
  fi

  # Prove we did something right
  echo "$(go version) installed in $GODIR: Go Binary: $(which go)"
else
  echo "$(go version) found in $GODIR: Go Binary: $(which go)"
fi

cd $ROOTDIR

# Configure the new go to be the first go found
export GOPATH=$ROOTDIR/.vendor
