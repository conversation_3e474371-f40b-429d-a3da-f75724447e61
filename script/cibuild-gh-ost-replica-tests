#!/bin/bash

set -e

whoami

# Clone gh-ost-ci-env
# Only clone if not already running locally at latest commit
remote_commit=$(git ls-remote https://github.com/github/gh-ost-ci-env.git HEAD | cut -f1)
local_commit="unknown"
[ -d "gh-ost-ci-env" ] && local_commit=$(cd gh-ost-ci-env && git log --format="%H" -n 1)

echo "remote commit is: $remote_commit"
echo "local commit is:  $local_commit"

if [ "$remote_commit" != "$local_commit" ] ; then
  rm -rf ./gh-ost-ci-env
  git clone https://github.com/github/gh-ost-ci-env.git
fi

test_mysql_version() {
  local mysql_version
  mysql_version="$1"

  echo "##### Testing $mysql_version"

  echo "### Setting up sandbox for $mysql_version"

  find sandboxes -name "stop_all" | bash

  mkdir -p sandbox/binary
  rm -rf sandbox/binary/*
  gh-ost-ci-env/bin/linux/dbdeployer unpack gh-ost-ci-env/mysql-tarballs/"$mysql_version".tar.gz --unpack-version="$mysql_version" --sandbox-binary ${PWD}/sandbox/binary

  mkdir -p sandboxes
  rm -rf sandboxes/*

  if echo "$mysql_version" | egrep "5[.]5[.]" ; then
    gtid=""
  else
    gtid="--gtid"
  fi
  gh-ost-ci-env/bin/linux/dbdeployer deploy replication "$mysql_version" --nodes 2 --sandbox-binary ${PWD}/sandbox/binary --sandbox-home ${PWD}/sandboxes ${gtid} --my-cnf-options log_slave_updates --my-cnf-options log_bin --my-cnf-options binlog_format=ROW --sandbox-directory rsandbox

  sed '/sandboxes/d' -i gh-ost-ci-env/bin/gh-ost-test-mysql-master
  echo 'sandboxes/rsandbox/m "$@"' >> gh-ost-ci-env/bin/gh-ost-test-mysql-master

  sed '/sandboxes/d' -i gh-ost-ci-env/bin/gh-ost-test-mysql-replica
  echo 'sandboxes/rsandbox/s1 "$@"' >> gh-ost-ci-env/bin/gh-ost-test-mysql-replica

  export PATH="${PWD}/gh-ost-ci-env/bin/:${PATH}"

  gh-ost-test-mysql-master -uroot -e "grant all on *.* to 'gh-ost'@'%' identified by 'gh-ost'"

  echo "### Running gh-ost tests for $mysql_version"
  ./localtests/test.sh -b bin/gh-ost

  find sandboxes -name "stop_all" | bash
}

echo "Building..."
. script/build
# Test all versions:
find gh-ost-ci-env/mysql-tarballs/ -name "*.tar.gz" | while read f ; do basename $f ".tar.gz" ; done | sort -r | while read mysql_version ; do
  test_mysql_version "$mysql_version"
done
